from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.rag_qa_prompt import (
    rag_qa_rerank_prompt,
    rag_qa_sys_prompt,
    rag_qa_user_prompt,
    rag_qa_strict_empty_sys_prompt,
    rag_qa_strict_nonempty_sys_prompt,
    rag_qa_common_empty_sys_prompt,
    rag_qa_common_nonempty_sys_prompt,
    rag_qa_strict_empty_user_prompt,
    rag_qa_strict_nonempty_user_prompt,
    rag_qa_common_empty_user_prompt,
    rag_qa_common_nonempty_user_prompt
)
from services.search_service import SearchService
from services.rerank_service import RerankService

from loguru import logger
from config.logging_config import configure_logging
from config.hardware_search_config import HARDWARE_SEARCH_COLLECTIONS
configure_logging()
import json
import asyncio


class RAGQA:
    """RAG问答类，支持流式和非流式输出"""
    
    def __init__(self, model_id: str, request_id: str = None):
        """
        初始化RAG问答实例
        
        Args:
            model_id: 模型ID (e.g. "gpt_4o", "qwen3_32b")
            request_id: 可选请求ID
        """
        self.model_id = model_id
        self.request_id = request_id
        # 实例化模型提供者
        self.provider = get_llm_provider(model_id, request_id)
        # 实例化搜索服务
        self.search_service = SearchService(request_id=request_id)
        # 实例化重排服务
        self.rerank_service = RerankService(request_id=request_id)
        self.logger = logger.bind(request_id=request_id)
        self.logger.info(f"RAGQA instance initialized with model_id: {model_id}")
    
    async def _retrieve_knowledge(self, query: str, user_id: str, collection_name: str, top_k: int = 20, min_score: float = None):
        """检索单个库并重排，不做top_r过滤"""
        self.logger.info(f"检索库: {collection_name}, 用户ID: {user_id}, 查询: {query}, top_k: {top_k}, min_score: {min_score}")
        search_results, error = await self.search_service.search(
            user_id=user_id,
            query=query,
            top_k=top_k,
            collection_name=collection_name
        )
        # print(f"search_results: {search_results}")
        if error or not search_results:
            return []
        self.logger.info(f"库 {collection_name} 检索到知识: {len(search_results)} 条")
        reranked_docs = await self.rerank_service.rerank_with_prompt(
            query=query,
            documents=search_results,
            instruction=rag_qa_rerank_prompt,
            top_r=top_k,
            min_score=min_score
        )
        self.logger.info(f"库 {collection_name} 重排后知识: {len(reranked_docs)} 条")
        return reranked_docs

    async def _retrieve_and_rerank_all_collections(self, query: str, user_id: str, top_k: int = 20, top_r: int = None, min_score: float = None):
        """异步检索所有collection并重排，最后统一排序取top_r"""
        tasks = []
        for collection in HARDWARE_SEARCH_COLLECTIONS:
            collection_name = collection.get("collection_name")
            if collection_name:
                tasks.append(self._retrieve_knowledge(query, user_id, collection_name, top_k=top_k, min_score=min_score))
        # 并发检索和重排
        all_results = await asyncio.gather(*tasks)
        # 合并所有库的重排结果
        merged_docs = [doc for docs in all_results for doc in docs]
        # 按score降序排序
        merged_docs.sort(key=lambda x: x.get("score", 0), reverse=True)
        # 只取前top_r
        if top_r is not None:
            merged_docs = merged_docs[:top_r]
        self.logger.info(f"所有库合并后重排知识: {len(merged_docs)} 条，top_r: {top_r}")
        return merged_docs

    def format_knowledge(self, reranked_docs):
        # print(f"格式化重排后的知识: {reranked_docs} ")
        """格式化重排后的知识为字符串"""
        formatted_docs = []
        for i, doc in enumerate(reranked_docs):
            formatted_doc = f"文档序号: {i+1}\n"
            formatted_doc += f"标题: {doc.get('title', '')}\n"
            formatted_doc += f"内容: {doc.get('content', '')}\n"
            # formatted_doc += f"文档名称: {doc.get('docName', '')}\n"
            formatted_doc += f"章节名称: {doc.get('sheetName', '')}\n"
            # formatted_doc += f"文档链接: {doc.get('docUrl', '')}\n"
            # formatted_doc += f"作者: {doc.get('owner', '')}\n"
            formatted_doc += f"更新时间: {doc.get('update_time', '')}\n"
            # formatted_doc += f"文档类型: {doc.get('doc_type', '')}\n"
            formatted_docs.append(formatted_doc)
            reranked_docs[i]["refNum"] = i+1
        self.logger.info(f"格式化后的知识, 共: {len(formatted_docs)}条")
        return "\n\n".join(formatted_docs), reranked_docs
    
    def _build_messages(self, query: str, history: List[Dict], knowledge: str, mode: str = "common") -> List[Dict]:
        """构建OpenAI格式的消息列表（适配新history格式）"""
        # 根据mode和knowledge是否为空选择对应的prompt
        is_knowledge_empty = not knowledge or knowledge.strip() == ""

        if mode == "strict":
            if is_knowledge_empty:
                self.logger.info(f"strict模式下, 知识为空")
                sys_prompt = rag_qa_strict_empty_sys_prompt
                user_prompt = rag_qa_strict_empty_user_prompt
            else:
                self.logger.info(f"strict模式下, 知识不为空")
                sys_prompt = rag_qa_strict_nonempty_sys_prompt
                user_prompt = rag_qa_strict_nonempty_user_prompt
        else:  # common mode
            if is_knowledge_empty:
                self.logger.info(f"common模式下, 知识为空")
                sys_prompt = rag_qa_common_empty_sys_prompt
                user_prompt = rag_qa_common_empty_user_prompt
            else:
                self.logger.info(f"common模式下, 知识不为空")
                sys_prompt = rag_qa_common_nonempty_sys_prompt
                user_prompt = rag_qa_common_nonempty_user_prompt

        messages = [{"role": "system", "content": sys_prompt}]
        for item in history[::-1]:
            # 安全检查：确保item是字典类型且包含必要的键
            if isinstance(item, dict) and "query" in item and "content" in item:
                messages.append({"role": "user", "content": item["query"]})
                messages.append({"role": "assistant", "content": item["content"]})
            else:
                # 记录无效的历史记录项
                self.logger.warning(f"跳过无效的历史记录项: {item}")
        formatted_query = user_prompt.replace("{{query}}", query).replace("{{body}}", knowledge)
        messages.append({"role": "user", "content": formatted_query})
        return messages
    
    def deduplicate_by_docurl(self,docs):
        seen = set()
        deduped = []
        for doc in docs:
            url = doc.get("docUrl")
            if url not in seen:
                deduped.append(doc)
                seen.add(url)
        return deduped
    

    
    async def generate_stream(
        self,
        query: str,
        user_id: str,
        history: List[Dict] = None,
        timeout: Optional[float] = None,
        top_k: int = 20,
        top_r: int = None,
        min_score: float = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = True,
        mode: str = "common",
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式RAG问答生成
        """
        if history is None:
            self.logger.info(f"历史对话为空，开始新对话")
            history = []
        self.logger.info(f"历史对话条数: {len(history)}")
        # 检索所有库并重排
        rerank_res = await self._retrieve_and_rerank_all_collections(query, user_id, top_k=top_k, top_r=top_r, min_score=min_score)
        knowledge, rerank_res = self.format_knowledge(rerank_res)
        self.logger.info(f"知识检索完成")
        self.logger.info(f"重排后的知识条数: {len(rerank_res)}")
        # rerank_res = self.deduplicate_by_docurl(rerank_res)
        # self.logger.info(f"去重后的知识条数: {len(rerank_res)}")
        
        yield {"type": "reference", "content": json.dumps(rerank_res, ensure_ascii=False), "role": "", "finish_reason": ""}
        print(f"knowledge: {rerank_res}")
        # # 安全检查：确保rerank_res不为空
        # if rerank_res and len(rerank_res) > 0:
        #     yield {"type": "reference", "content": json.dumps([rerank_res[0]], ensure_ascii=False), "role": "", "finish_reason": ""}
        # else:
        #     yield {"type": "reference", "content": json.dumps([], ensure_ascii=False), "role": "", "finish_reason": ""}
        
        # rerank_res_str =  json.dumps(rerank_res, ensure_ascii=False)
        # self.logger.info(f"知识检索结果格式化处理")
        # 构建消息
        messages = self._build_messages(query, history, knowledge, mode)
        self.logger.info(f"开始调用模型服务")
        # print(f"构建消息：{messages}")

        # 模型服务的流式调用
        async for chunk in self.provider.generate_stream(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            temperature=temperature,
            top_p=top_p,
            **kwargs
        ):
            yield chunk